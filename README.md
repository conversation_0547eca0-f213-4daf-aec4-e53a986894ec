# Hex ERP - Odoo 17 项目

这是一个基于 Odoo 17 的企业资源规划系统，包含多个自定义插件和第三方插件。

## 项目结构

```
hex-erp/
├── my-addons/          # 自定义插件
├── buy-addons/         # 购买的插件
├── free-addons/        # 免费插件
├── enterprise-addons/  # 企业版插件
├── filestore/          # 文件存储
├── logs/              # 日志文件
├── venv/              # Python 虚拟环境
├── odoo.conf          # Odoo 配置文件
├── setup.sh           # 环境设置脚本
├── start_odoo.sh      # 启动脚本
└── requirements.txt   # Python 依赖
```

## 快速开始

### 1. 环境设置

运行设置脚本：
```bash
./setup.sh
```

### 2. 安装 PostgreSQL

由于 macOS 版本问题，需要手动安装 PostgreSQL：

**选项 1: 使用 Postgres.app**
1. 访问 https://postgresapp.com/
2. 下载并安装 Postgres.app
3. 启动应用程序

**选项 2: 使用官方安装包**
1. 访问 https://www.postgresql.org/download/macosx/
2. 下载并安装 PostgreSQL

### 3. 创建数据库用户和数据库

```bash
# 创建 odoo 用户
createuser -s odoo

# 创建数据库
createdb -O odoo hex_erp
```

### 4. 下载 Odoo 17 源码

如果 setup.sh 脚本无法自动下载，请手动下载：

```bash
git clone --depth 1 --branch 17.0 https://github.com/odoo/odoo.git odoo-17
```

或者从 GitHub 下载 ZIP 文件并解压到 `odoo-17` 目录。

### 5. 启动系统

```bash
./start_odoo.sh
```

## 配置说明

### odoo.conf 配置文件

主要配置项：
- `db_host`: 数据库主机 (默认: localhost)
- `db_port`: 数据库端口 (默认: 5432)
- `db_user`: 数据库用户 (默认: odoo)
- `db_password`: 数据库密码 (默认: odoo)
- `db_name`: 数据库名称 (默认: hex_erp)
- `http_port`: Web 服务端口 (默认: 8069)
- `addons_path`: 插件路径

### 插件说明

- **my-addons**: 自定义开发的插件
- **buy-addons**: 购买的商业插件
- **free-addons**: 免费的第三方插件
- **enterprise-addons**: Odoo 企业版插件

## 访问系统

启动成功后，在浏览器中访问：
```
http://localhost:8069
```

默认管理员密码：`admin123`

## 故障排除

### 1. 数据库连接失败
- 确保 PostgreSQL 正在运行
- 检查数据库用户和密码是否正确
- 确认数据库已创建

### 2. 插件加载失败
- 检查插件路径是否正确
- 确认插件依赖是否已安装

### 3. 端口冲突
- 修改 `odoo.conf` 中的 `http_port` 配置

## 开发说明

### 添加新插件
1. 将插件放入相应的 addons 目录
2. 重启 Odoo 服务
3. 在应用管理中安装插件

### 日志查看
日志文件位置：`./logs/odoo.log`

### 数据备份
```bash
pg_dump hex_erp > backup.sql
```

## 联系信息

如有问题，请联系开发团队。
