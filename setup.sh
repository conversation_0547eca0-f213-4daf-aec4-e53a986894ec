#!/bin/bash

# Hex ERP 环境设置脚本

echo "=== Hex ERP (Odoo17) 环境设置 ==="

# 检查 Python
echo "检查 Python 版本..."
python3 --version
if [ $? -ne 0 ]; then
    echo "错误: Python3 未安装"
    exit 1
fi

# 检查并安装 PostgreSQL
echo "检查 PostgreSQL..."
if ! command -v psql &> /dev/null; then
    echo "PostgreSQL 未安装，正在安装..."
    
    # 对于 macOS 15，尝试使用 MacPorts 或直接下载
    echo "由于 macOS 版本问题，请手动安装 PostgreSQL:"
    echo "1. 访问 https://postgresapp.com/ 下载 Postgres.app"
    echo "2. 或者访问 https://www.postgresql.org/download/macosx/"
    echo "3. 安装完成后，创建 odoo 用户:"
    echo "   createuser -s odoo"
    echo "   createdb -O odoo hex_erp"
    echo ""
    echo "然后重新运行此脚本"
    exit 1
else
    echo "PostgreSQL 已安装"
fi

# 检查虚拟环境
if [ ! -d "venv" ]; then
    echo "创建虚拟环境..."
    python3 -m venv venv
fi

# 激活虚拟环境并安装依赖
echo "激活虚拟环境并安装依赖..."
source venv/bin/activate
pip install --upgrade pip

# 安装项目依赖
if [ -f "requirements.txt" ]; then
    echo "安装项目依赖..."
    pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org -r requirements.txt
fi

# 安装 Odoo 依赖
echo "安装 Odoo 依赖..."
pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org \
    psycopg2-binary lxml decorator Werkzeug Jinja2 passlib Babel python-dateutil \
    psutil reportlab PyPDF2 qrcode vobject xlwt pytz pyusb greenlet xlrd xlsxwriter chardet

# 下载 Odoo 17
if [ ! -d "odoo-17" ]; then
    echo "下载 Odoo 17 源码..."
    git clone --depth 1 --branch 17.0 https://github.com/odoo/odoo.git odoo-17
    if [ $? -ne 0 ]; then
        echo "Git 下载失败，请手动下载:"
        echo "git clone --depth 1 --branch 17.0 https://github.com/odoo/odoo.git odoo-17"
        echo "或者从 https://github.com/odoo/odoo/archive/refs/heads/17.0.zip 下载并解压"
    fi
else
    echo "Odoo 17 源码已存在"
fi

# 创建必要的目录
mkdir -p logs
mkdir -p filestore

echo ""
echo "=== 设置完成 ==="
echo "下一步:"
echo "1. 确保 PostgreSQL 正在运行"
echo "2. 创建数据库用户和数据库:"
echo "   createuser -s odoo"
echo "   createdb -O odoo hex_erp"
echo "3. 运行 ./start_odoo.sh 启动系统"
echo ""
