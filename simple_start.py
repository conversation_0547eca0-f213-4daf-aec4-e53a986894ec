#!/usr/bin/env python3
"""
简化的 Odoo 启动脚本
当无法下载完整 Odoo 源码时使用
"""

import os
import sys
import subprocess

def check_requirements():
    """检查基本要求"""
    print("检查环境要求...")
    
    # 检查 Python 版本
    if sys.version_info < (3, 8):
        print("错误: 需要 Python 3.8 或更高版本")
        return False
    
    # 检查虚拟环境
    if not os.path.exists('venv'):
        print("错误: 虚拟环境不存在，请运行 setup.sh")
        return False
    
    # 检查必要的包
    try:
        import psycopg2
        import lxml
        import werkzeug
        print("✓ 基本依赖已安装")
    except ImportError as e:
        print(f"错误: 缺少依赖包 {e}")
        return False
    
    return True

def install_odoo_minimal():
    """安装最小化的 Odoo"""
    print("尝试安装 Odoo...")
    
    # 尝试从 PyPI 安装（可能版本不是 17.0）
    try:
        subprocess.run([
            sys.executable, '-m', 'pip', 'install', 
            '--trusted-host', 'pypi.org',
            '--trusted-host', 'pypi.python.org', 
            '--trusted-host', 'files.pythonhosted.org',
            'odoo'
        ], check=True)
        print("✓ Odoo 安装成功")
        return True
    except subprocess.CalledProcessError:
        print("× Odoo 安装失败")
        return False

def create_minimal_config():
    """创建最小配置"""
    config_content = """[options]
# 数据库配置
db_host = localhost
db_port = 5432
db_user = odoo
db_password = odoo

# 服务器配置
http_port = 8069
workers = 0

# 插件路径配置 - 只使用现有插件
addons_path = ./my-addons,./buy-addons,./free-addons

# 数据目录
data_dir = ./filestore

# 日志配置
logfile = ./logs/odoo.log
log_level = info

# 安全配置
admin_passwd = admin123

# 其他配置
without_demo = True
list_db = True
"""
    
    with open('odoo_minimal.conf', 'w') as f:
        f.write(config_content)
    
    print("✓ 创建最小配置文件: odoo_minimal.conf")

def main():
    """主函数"""
    print("=== Hex ERP 简化启动 ===")
    
    if not check_requirements():
        print("\n请先运行 setup.sh 安装依赖")
        return 1
    
    # 激活虚拟环境
    venv_python = os.path.join('venv', 'bin', 'python3')
    if not os.path.exists(venv_python):
        print("错误: 虚拟环境 Python 不存在")
        return 1
    
    # 尝试安装 Odoo
    if not install_odoo_minimal():
        print("\n无法安装 Odoo，请手动下载 Odoo 17 源码:")
        print("1. 访问 https://github.com/odoo/odoo/archive/refs/heads/17.0.zip")
        print("2. 下载并解压到 odoo-17 目录")
        print("3. 然后运行 ./start_odoo.sh")
        return 1
    
    # 创建配置
    create_minimal_config()
    
    # 创建必要目录
    os.makedirs('logs', exist_ok=True)
    os.makedirs('filestore', exist_ok=True)
    
    print("\n=== 启动说明 ===")
    print("1. 确保 PostgreSQL 正在运行")
    print("2. 创建数据库:")
    print("   createuser -s odoo")
    print("   createdb -O odoo hex_erp")
    print("3. 启动 Odoo:")
    print("   source venv/bin/activate")
    print("   odoo -c odoo_minimal.conf")
    print("4. 访问 http://localhost:8069")
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
