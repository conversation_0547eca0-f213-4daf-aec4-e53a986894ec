#!/bin/bash

# Hex ERP Odoo17 启动脚本

echo "正在启动 Hex ERP (Odoo17)..."

# 检查虚拟环境
if [ ! -d "venv" ]; then
    echo "错误: 虚拟环境不存在，请先运行 setup.sh"
    exit 1
fi

# 检查 Odoo 源码
if [ ! -d "odoo-17" ]; then
    echo "错误: Odoo 源码不存在，请先下载 Odoo 17"
    echo "运行以下命令下载 Odoo 17:"
    echo "git clone --depth 1 --branch 17.0 https://github.com/odoo/odoo.git odoo-17"
    exit 1
fi

# 检查配置文件
if [ ! -f "odoo.conf" ]; then
    echo "错误: 配置文件 odoo.conf 不存在"
    exit 1
fi

# 创建日志目录
mkdir -p logs

# 激活虚拟环境
source venv/bin/activate

# 检查数据库连接
echo "检查数据库连接..."
python3 -c "
import psycopg2
try:
    conn = psycopg2.connect(host='localhost', port=5432, user='odoo', password='odoo', dbname='postgres')
    print('数据库连接成功')
    conn.close()
except Exception as e:
    print(f'数据库连接失败: {e}')
    print('请确保 PostgreSQL 已安装并运行，且已创建 odoo 用户')
    exit(1)
"

if [ $? -ne 0 ]; then
    echo "数据库连接失败，请检查 PostgreSQL 配置"
    exit 1
fi

# 启动 Odoo
echo "启动 Odoo..."
python3 odoo-17/odoo-bin -c odoo.conf

echo "Odoo 已停止"
