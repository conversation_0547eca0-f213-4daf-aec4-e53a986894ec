2025-06-09 11:06:40,053 1 INFO ? odoo: Odoo version 17.0-20250606 
2025-06-09 11:06:40,053 1 INFO ? odoo: Using configuration file at /etc/odoo/odoo.conf 
2025-06-09 11:06:40,053 1 INFO ? odoo: addons paths: ['/usr/lib/python3/dist-packages/odoo/addons', '/var/lib/odoo/addons/17.0', '/mnt/extra-addons/my-addons', '/mnt/extra-addons/buy-addons', '/mnt/extra-addons/free-addons', '/mnt/extra-addons/enterprise-addons'] 
2025-06-09 11:06:40,053 1 INFO ? odoo: database: odoo@db:5432 
2025-06-09 11:06:40,132 1 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at /usr/local/bin/wkhtmltopdf 
2025-06-09 11:06:40,289 1 INFO ? odoo.service.server: HTTP service (werkzeug) running on 0.0.0.0:8069 
2025-06-09 11:06:40,308 1 ERROR hex_erp odoo.modules.loading: Database hex_erp not initialized, you can force it with `-i base` 
2025-06-09 11:06:40,308 1 INFO hex_erp odoo.modules.registry: Registry loaded in 0.018s 
2025-06-09 11:06:40,308 1 INFO hex_erp odoo.sql_db: ConnectionPool(used=0/count=0/max=64): Closed 1 connections  
2025-06-09 11:06:40,328 30 INFO hex_erp odoo.service.server: Worker WorkerHTTP (30) alive 
2025-06-09 11:06:40,340 32 INFO hex_erp odoo.service.server: Worker WorkerHTTP (32) alive 
2025-06-09 11:06:40,345 31 INFO hex_erp odoo.service.server: Worker WorkerHTTP (31) alive 
2025-06-09 11:06:40,364 34 INFO hex_erp odoo.service.server: Worker WorkerHTTP (34) alive 
2025-06-09 11:06:40,375 39 INFO hex_erp odoo.service.server: Worker WorkerCron (39) alive 
2025-06-09 11:06:40,383 37 INFO hex_erp odoo.service.server: Worker WorkerCron (37) alive 
2025-06-09 11:06:40,974 35 INFO ? odoo: Odoo version 17.0-20250606 
2025-06-09 11:06:40,975 35 INFO ? odoo: Using configuration file at /etc/odoo/odoo.conf 
2025-06-09 11:06:40,975 35 INFO ? odoo: addons paths: ['/usr/lib/python3/dist-packages/odoo/addons', '/var/lib/odoo/addons/17.0', '/mnt/extra-addons/my-addons', '/mnt/extra-addons/buy-addons', '/mnt/extra-addons/free-addons', '/mnt/extra-addons/enterprise-addons'] 
2025-06-09 11:06:40,975 35 INFO ? odoo: database: odoo@db:5432 
2025-06-09 11:06:41,080 35 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at /usr/local/bin/wkhtmltopdf 
2025-06-09 11:06:41,237 35 INFO ? odoo.service.server: Evented Service (longpolling) running on 0.0.0.0:8072 
2025-06-09 11:07:45,904 32 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "/usr/lib/python3/dist-packages/odoo/tools/config.py", line 790, in session_dir
    os.makedirs(d, 0o700)
  File "/usr/lib/python3.10/os.py", line 225, in makedirs
    mkdir(name, mode)
FileExistsError: [Errno 17] File exists: '/var/lib/odoo/sessions'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3/dist-packages/odoo/http.py", line 2221, in __call__
    request._post_init()
  File "/usr/lib/python3/dist-packages/odoo/http.py", line 1406, in _post_init
    self.session, self.db = self._get_session_and_dbname()
  File "/usr/lib/python3/dist-packages/odoo/http.py", line 1412, in _get_session_and_dbname
    session = root.session_store.new()
  File "/usr/lib/python3/dist-packages/odoo/tools/func.py", line 28, in __get__
    value = self.fget(obj)
  File "/usr/lib/python3/dist-packages/odoo/http.py", line 2147, in session_store
    path = odoo.tools.config.session_dir
  File "/usr/lib/python3/dist-packages/odoo/tools/config.py", line 794, in session_dir
    assert os.access(d, os.W_OK), \
AssertionError: /var/lib/odoo/sessions: directory is not writable
2025-06-09 11:07:45,920 32 INFO ? werkzeug: 192.168.65.1 - - [09/Jun/2025 11:07:45] "HEAD / HTTP/1.1" 500 - 0 0.000 0.030
2025-06-09 11:07:47,579 37 ERROR hex_erp odoo.sql_db: bad query: 
            SELECT latest_version
            FROM ir_module_module
             WHERE name='base'
        
ERROR: relation "ir_module_module" does not exist
LINE 3:             FROM ir_module_module
                         ^
 
2025-06-09 11:07:47,582 37 WARNING hex_erp odoo.addons.base.models.ir_cron: Tried to poll an undefined table on database hex_erp. 
2025-06-09 11:07:49,584 39 ERROR hex_erp odoo.sql_db: bad query: 
            SELECT latest_version
            FROM ir_module_module
             WHERE name='base'
        
ERROR: relation "ir_module_module" does not exist
LINE 3:             FROM ir_module_module
                         ^
 
2025-06-09 11:07:49,586 39 WARNING hex_erp odoo.addons.base.models.ir_cron: Tried to poll an undefined table on database hex_erp. 
2025-06-09 11:08:11,356 53 INFO ? odoo: Odoo version 17.0-20250606 
2025-06-09 11:08:11,356 53 INFO ? odoo: Using configuration file at /etc/odoo/odoo.conf 
2025-06-09 11:08:11,356 53 INFO ? odoo: addons paths: ['/usr/lib/python3/dist-packages/odoo/addons', '/var/lib/odoo/addons/17.0', '/mnt/extra-addons/my-addons', '/mnt/extra-addons/buy-addons', '/mnt/extra-addons/free-addons', '/mnt/extra-addons/enterprise-addons'] 
2025-06-09 11:08:11,356 53 INFO ? odoo: database: odoo@db:5432 
2025-06-09 11:08:11,469 53 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at /usr/local/bin/wkhtmltopdf 
2025-06-09 11:08:11,649 53 INFO ? odoo.service.server: HTTP service (werkzeug) running on 0.0.0.0:8069 
2025-06-09 11:08:20,610 1 INFO hex_erp odoo.service.server: Stopping gracefully 
2025-06-09 11:08:20,614 32 INFO hex_erp odoo.service.server: Worker (32) exiting. request_count: 1, registry count: 1. 
2025-06-09 11:08:20,614 30 INFO hex_erp odoo.service.server: Worker (30) exiting. request_count: 0, registry count: 1. 
2025-06-09 11:08:20,614 34 INFO hex_erp odoo.service.server: Worker (34) exiting. request_count: 0, registry count: 1. 
2025-06-09 11:08:20,614 31 INFO hex_erp odoo.service.server: Worker (31) exiting. request_count: 0, registry count: 1. 
2025-06-09 11:08:20,685 37 INFO hex_erp odoo.service.server: Worker (37) exiting. request_count: 1, registry count: 1. 
2025-06-09 11:08:20,707 39 INFO hex_erp odoo.service.server: Worker (39) exiting. request_count: 1, registry count: 1. 
2025-06-09 11:08:42,407 1 INFO ? odoo: Odoo version 17.0-20250606 
2025-06-09 11:08:42,407 1 INFO ? odoo: Using configuration file at /etc/odoo/odoo.conf 
2025-06-09 11:08:42,408 1 INFO ? odoo: addons paths: ['/usr/lib/python3/dist-packages/odoo/addons', '/var/lib/odoo/addons/17.0', '/mnt/extra-addons/my-addons', '/mnt/extra-addons/buy-addons', '/mnt/extra-addons/free-addons', '/mnt/extra-addons/enterprise-addons'] 
2025-06-09 11:08:42,408 1 INFO ? odoo: database: odoo@db:5432 
2025-06-09 11:08:42,476 1 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at /usr/local/bin/wkhtmltopdf 
2025-06-09 11:08:42,563 1 INFO ? odoo.service.server: HTTP service (werkzeug) running on 0.0.0.0:8069 
2025-06-09 11:08:42,588 1 INFO hex_erp odoo.modules.loading: init db 
2025-06-09 11:08:42,692 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'account_bank_statement_china', defaulting to LGPL-3 
2025-06-09 11:08:42,697 1 WARNING hex_erp py.warnings: <unknown>:8: DeprecationWarning: invalid escape sequence '\ '
  File "/usr/bin/odoo", line 8, in <module>
    odoo.cli.main()
  File "/usr/lib/python3/dist-packages/odoo/cli/command.py", line 66, in main
    o.run(args)
  File "/usr/lib/python3/dist-packages/odoo/cli/server.py", line 186, in run
    main(args)
  File "/usr/lib/python3/dist-packages/odoo/cli/server.py", line 179, in main
    rc = odoo.service.server.start(preload=preload, stop=stop)
  File "/usr/lib/python3/dist-packages/odoo/service/server.py", line 1469, in start
    rc = server.run(preload, stop)
  File "/usr/lib/python3/dist-packages/odoo/service/server.py", line 1018, in run
    rc = preload_registries(preload)
  File "/usr/lib/python3/dist-packages/odoo/service/server.py", line 1369, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "<decorator-gen-16>", line 2, in new
  File "/usr/lib/python3/dist-packages/odoo/tools/func.py", line 87, in locked
    return func(inst, *args, **kwargs)
  File "/usr/lib/python3/dist-packages/odoo/modules/registry.py", line 110, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "/usr/lib/python3/dist-packages/odoo/modules/loading.py", line 399, in load_modules
    odoo.modules.db.initialize(cr)
  File "/usr/lib/python3/dist-packages/odoo/modules/db.py", line 44, in initialize
    info = odoo.modules.get_manifest(i)
  File "/usr/lib/python3/dist-packages/odoo/modules/module.py", line 369, in get_manifest
    return copy.deepcopy(_get_manifest_cached(module, mod_path))
  File "/usr/lib/python3/dist-packages/odoo/modules/module.py", line 373, in _get_manifest_cached
    return load_manifest(module, mod_path)
  File "/usr/lib/python3/dist-packages/odoo/modules/module.py", line 319, in load_manifest
    manifest.update(ast.literal_eval(f.read()))
  File "/usr/lib/python3/dist-packages/odoo/tools/_monkeypatches.py", line 88, in literal_eval
    return orig_literal_eval(expr)
  File "/usr/lib/python3.10/ast.py", line 64, in literal_eval
    node_or_string = parse(node_or_string.lstrip(" \t"), mode='eval')
  File "/usr/lib/python3.10/ast.py", line 50, in parse
    return compile(source, filename, mode, flags,
  File "/usr/lib/python3.10/warnings.py", line 109, in _showwarnmsg
    sw(msg.message, msg.category, msg.filename, msg.lineno,
  File "/usr/lib/python3/dist-packages/odoo/netsvc.py", line 321, in showwarning_with_traceback
    for frame in traceback.extract_stack():
 
2025-06-09 11:08:42,711 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'account_ledger_inherit', defaulting to LGPL-3 
2025-06-09 11:08:42,713 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'account_payment_approval', defaulting to LGPL-3 
2025-06-09 11:08:42,738 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'approvals_account_move', defaulting to LGPL-3 
2025-06-09 11:08:42,739 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'approvals_inherit', defaulting to LGPL-3 
2025-06-09 11:08:42,745 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'approvals_stock_picking', defaulting to LGPL-3 
2025-06-09 11:08:42,752 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'attachment_minio', defaulting to LGPL-3 
2025-06-09 11:08:42,757 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'auditlog_async', defaulting to LGPL-3 
2025-06-09 11:08:42,788 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'base_field_rights', defaulting to LGPL-3 
2025-06-09 11:08:42,821 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'business_payment', defaulting to LGPL-3 
2025-06-09 11:08:42,824 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'business_trip', defaulting to LGPL-3 
2025-06-09 11:08:42,830 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'chatter_attachment_display', defaulting to LGPL-3 
2025-06-09 11:08:42,836 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'command_global_search', defaulting to LGPL-3 
2025-06-09 11:08:42,841 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'credit_card_analysis', defaulting to LGPL-3 
2025-06-09 11:08:42,852 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'custom_list_view', defaulting to LGPL-3 
2025-06-09 11:08:42,854 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'customer_default_discount', defaulting to LGPL-3 
2025-06-09 11:08:42,859 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'delegate_outside_order', defaulting to LGPL-3 
2025-06-09 11:08:42,895 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'expense_reimburse', defaulting to LGPL-3 
2025-06-09 11:08:42,897 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'expense_reimburse_inherit', defaulting to LGPL-3 
2025-06-09 11:08:42,903 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'feishu_inherit', defaulting to LGPL-3 
2025-06-09 11:08:42,905 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'file_convert_by_unoconv', defaulting to LGPL-3 
2025-06-09 11:08:42,906 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'financial_report', defaulting to LGPL-3 
2025-06-09 11:08:42,987 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'import_configration', defaulting to LGPL-3 
2025-06-09 11:08:42,998 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'izi_dashboard_inherit', defaulting to LGPL-3 
2025-06-09 11:08:43,002 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'jgq_reportbro', defaulting to LGPL-3 
2025-06-09 11:08:43,341 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'melgeek_echarts', defaulting to LGPL-3 
2025-06-09 11:08:43,342 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'melgeek_product_sale_report', defaulting to LGPL-3 
2025-06-09 11:08:43,344 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'melgeek_purchase_report', defaulting to LGPL-3 
2025-06-09 11:08:43,347 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'menu_extend', defaulting to LGPL-3 
2025-06-09 11:08:43,351 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'mg_partner', defaulting to LGPL-3 
2025-06-09 11:08:43,354 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'mg_stock_inherit', defaulting to LGPL-3 
2025-06-09 11:08:43,394 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'my_debug', defaulting to LGPL-3 
2025-06-09 11:08:43,420 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'onchange_return_customize', defaulting to LGPL-3 
2025-06-09 11:08:43,496 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'product_category_account', defaulting to LGPL-3 
2025-06-09 11:08:43,501 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'product_limit_access', defaulting to LGPL-3 
2025-06-09 11:08:43,565 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'remove_business_data', defaulting to LGPL-3 
2025-06-09 11:08:43,572 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'ro_reconciliation_order', defaulting to LGPL-3 
2025-06-09 11:08:43,594 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'sale_purchase_advanced_with_contact', defaulting to LGPL-3 
2025-06-09 11:08:43,772 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'udoo_om_ux_inherit', defaulting to LGPL-3 
2025-06-09 11:08:43,779 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'values_auto_create_field', defaulting to LGPL-3 
2025-06-09 11:08:43,787 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'web_approval_inherit', defaulting to LGPL-3 
2025-06-09 11:08:43,881 1 WARNING hex_erp odoo.modules.module: Missing `license` key in manifest for 'xlsx_preview_in_chatter', defaulting to LGPL-3 
2025-06-09 11:08:43,890 1 INFO hex_erp odoo.modules.loading: loading 1 modules... 
2025-06-09 11:08:43,890 1 INFO hex_erp odoo.modules.loading: Loading module base (1/1) 
2025-06-09 11:08:43,903 1 INFO hex_erp odoo.modules.registry: module base: creating or updating database tables 
2025-06-09 11:08:44,089 1 INFO hex_erp odoo.models: Prepare computation of ir.module.module.menus_by_module 
2025-06-09 11:08:44,089 1 INFO hex_erp odoo.models: Prepare computation of ir.module.module.reports_by_module 
2025-06-09 11:08:44,090 1 INFO hex_erp odoo.models: Prepare computation of ir.module.module.views_by_module 
2025-06-09 11:08:44,154 1 INFO hex_erp odoo.models: Prepare computation of res.partner.user_id 
2025-06-09 11:08:44,154 1 INFO hex_erp odoo.models: Prepare computation of res.partner.commercial_partner_id 
2025-06-09 11:08:44,154 1 INFO hex_erp odoo.models: Prepare computation of res.partner.complete_name 
2025-06-09 11:08:44,154 1 INFO hex_erp odoo.models: Prepare computation of res.partner.company_registry 
2025-06-09 11:08:44,154 1 INFO hex_erp odoo.models: Prepare computation of res.partner.commercial_company_name 
2025-06-09 11:08:44,155 1 INFO hex_erp odoo.models: Prepare computation of res.partner.partner_share 
2025-06-09 11:08:44,176 1 INFO hex_erp odoo.models: Prepare computation of res.currency.decimal_places 
2025-06-09 11:08:44,194 1 INFO hex_erp odoo.models: Prepare computation of res.company.uses_default_logo 
2025-06-09 11:08:44,194 1 INFO hex_erp odoo.models: Prepare computation of res.company.logo_web 
2025-06-09 11:08:44,196 1 INFO hex_erp odoo.models: Computing parent_path for table res_company... 
2025-06-09 11:08:44,214 1 INFO hex_erp odoo.models: Prepare computation of res.users.signature 
2025-06-09 11:08:44,214 1 INFO hex_erp odoo.models: Prepare computation of res.users.share 
2025-06-09 11:08:44,971 1 INFO hex_erp odoo.modules.loading: loading base/data/res_bank.xml 
2025-06-09 11:08:44,974 1 INFO hex_erp odoo.modules.loading: loading base/data/res.lang.csv 
2025-06-09 11:08:45,020 1 INFO hex_erp odoo.modules.loading: loading base/data/res_lang_data.xml 
2025-06-09 11:08:45,039 1 INFO hex_erp odoo.modules.loading: loading base/data/res_partner_data.xml 
2025-06-09 11:08:45,072 1 INFO hex_erp odoo.modules.loading: loading base/data/res_currency_data.xml 
2025-06-09 11:08:45,337 1 INFO hex_erp odoo.modules.loading: loading base/data/res_company_data.xml 
2025-06-09 11:08:45,343 1 INFO hex_erp odoo.modules.loading: loading base/data/res_users_data.xml 
2025-06-09 11:08:46,100 1 INFO hex_erp odoo.modules.loading: loading base/data/report_paperformat_data.xml 
2025-06-09 11:08:46,104 1 INFO hex_erp odoo.modules.loading: loading base/data/res_country_data.xml 
2025-06-09 11:08:46,298 1 INFO hex_erp odoo.modules.loading: loading base/data/ir_demo_data.xml 
2025-06-09 11:08:46,304 1 INFO hex_erp odoo.modules.loading: loading base/security/base_groups.xml 
2025-06-09 11:08:46,535 1 INFO hex_erp odoo.modules.loading: loading base/security/base_security.xml 
2025-06-09 11:08:46,607 1 INFO hex_erp odoo.modules.loading: loading base/views/base_menus.xml 
2025-06-09 11:08:46,646 1 INFO hex_erp odoo.modules.loading: loading base/views/decimal_precision_views.xml 
2025-06-09 11:08:46,655 1 INFO hex_erp odoo.modules.loading: loading base/views/res_config_views.xml 
2025-06-09 11:08:46,660 1 INFO hex_erp odoo.modules.loading: loading base/data/res.country.state.csv 
2025-06-09 11:08:46,895 1 INFO hex_erp odoo.modules.loading: loading base/views/ir_actions_views.xml 
2025-06-09 11:08:46,953 1 INFO hex_erp odoo.modules.loading: loading base/views/ir_asset_views.xml 
2025-06-09 11:08:46,962 1 INFO hex_erp odoo.modules.loading: loading base/views/ir_config_parameter_views.xml 
2025-06-09 11:08:46,969 1 INFO hex_erp odoo.modules.loading: loading base/views/ir_cron_views.xml 
2025-06-09 11:08:46,984 1 INFO hex_erp odoo.modules.loading: loading base/views/ir_cron_trigger_views.xml 
2025-06-09 11:08:46,995 1 INFO hex_erp odoo.modules.loading: loading base/views/ir_filters_views.xml 
2025-06-09 11:08:47,006 1 INFO hex_erp odoo.modules.loading: loading base/views/ir_mail_server_views.xml 
2025-06-09 11:08:47,017 1 INFO hex_erp odoo.modules.loading: loading base/views/ir_model_views.xml 
2025-06-09 11:08:47,087 1 INFO hex_erp odoo.modules.loading: loading base/views/ir_attachment_views.xml 
2025-06-09 11:08:47,099 1 INFO hex_erp odoo.modules.loading: loading base/views/ir_rule_views.xml 
2025-06-09 11:08:47,116 1 INFO hex_erp odoo.modules.loading: loading base/views/ir_sequence_views.xml 
2025-06-09 11:08:47,126 1 INFO hex_erp odoo.modules.loading: loading base/views/ir_ui_menu_views.xml 
2025-06-09 11:08:47,136 1 INFO hex_erp odoo.modules.loading: loading base/views/ir_ui_view_views.xml 
2025-06-09 11:08:47,164 1 INFO hex_erp odoo.modules.loading: loading base/views/ir_default_views.xml 
2025-06-09 11:08:47,174 1 INFO hex_erp odoo.modules.loading: loading base/data/ir_cron_data.xml 
2025-06-09 11:08:47,180 1 INFO hex_erp odoo.modules.loading: loading base/report/ir_model_report.xml 
2025-06-09 11:08:47,183 1 INFO hex_erp odoo.modules.loading: loading base/report/ir_model_templates.xml 
2025-06-09 11:08:47,187 1 INFO hex_erp odoo.modules.loading: loading base/views/ir_logging_views.xml 
2025-06-09 11:08:47,197 1 INFO hex_erp odoo.modules.loading: loading base/views/ir_qweb_widget_templates.xml 
2025-06-09 11:08:47,203 1 INFO hex_erp odoo.modules.loading: loading base/views/ir_module_views.xml 
2025-06-09 11:08:47,230 1 INFO hex_erp odoo.modules.loading: loading base/data/ir_module_category_data.xml 
2025-06-09 11:08:47,268 1 INFO hex_erp odoo.modules.loading: loading base/data/ir_module_module.xml 
2025-06-09 11:08:47,300 1 INFO hex_erp odoo.modules.loading: loading base/report/ir_module_reports.xml 
2025-06-09 11:08:47,302 1 INFO hex_erp odoo.modules.loading: loading base/report/ir_module_report_templates.xml 
2025-06-09 11:08:47,305 1 INFO hex_erp odoo.modules.loading: loading base/wizard/base_module_update_views.xml 
2025-06-09 11:08:47,311 1 INFO hex_erp odoo.modules.loading: loading base/wizard/base_language_install_views.xml 
2025-06-09 11:08:47,316 1 INFO hex_erp odoo.modules.loading: loading base/wizard/base_import_language_views.xml 
2025-06-09 11:08:47,321 1 INFO hex_erp odoo.modules.loading: loading base/wizard/base_module_upgrade_views.xml 
2025-06-09 11:08:47,329 1 INFO hex_erp odoo.modules.loading: loading base/wizard/base_module_uninstall_views.xml 
2025-06-09 11:08:47,333 1 INFO hex_erp odoo.modules.loading: loading base/wizard/base_export_language_views.xml 
2025-06-09 11:08:47,339 1 INFO hex_erp odoo.modules.loading: loading base/wizard/base_partner_merge_views.xml 
2025-06-09 11:08:47,345 1 INFO hex_erp odoo.modules.loading: loading base/data/ir_demo_failure_data.xml 
2025-06-09 11:08:47,352 1 INFO hex_erp odoo.modules.loading: loading base/views/ir_profile_views.xml 
2025-06-09 11:08:47,363 1 INFO hex_erp odoo.modules.loading: loading base/views/res_company_views.xml 
2025-06-09 11:08:47,373 1 INFO hex_erp odoo.modules.loading: loading base/views/res_lang_views.xml 
2025-06-09 11:08:47,385 1 INFO hex_erp odoo.modules.loading: loading base/views/res_partner_views.xml 
2025-06-09 11:08:47,443 1 INFO hex_erp odoo.modules.loading: loading base/views/res_bank_views.xml 
2025-06-09 11:08:47,459 1 INFO hex_erp odoo.modules.loading: loading base/views/res_country_views.xml 
2025-06-09 11:08:47,476 1 INFO hex_erp odoo.modules.loading: loading base/views/res_currency_views.xml 
2025-06-09 11:08:47,492 1 INFO hex_erp odoo.modules.loading: loading base/views/res_users_views.xml 
2025-06-09 11:08:47,543 1 INFO hex_erp odoo.modules.loading: loading base/views/res_users_identitycheck_views.xml 
2025-06-09 11:08:47,547 1 INFO hex_erp odoo.modules.loading: loading base/views/ir_property_views.xml 
2025-06-09 11:08:47,555 1 INFO hex_erp odoo.modules.loading: loading base/views/res_config_settings_views.xml 
2025-06-09 11:08:47,559 1 INFO hex_erp odoo.modules.loading: loading base/views/report_paperformat_views.xml 
2025-06-09 11:08:47,571 1 INFO hex_erp odoo.modules.loading: loading base/security/ir.model.access.csv 
2025-06-09 11:08:47,642 1 INFO hex_erp odoo.modules.loading: Module base loaded in 3.75s, 7930 queries (+7930 other) 
2025-06-09 11:08:47,642 1 INFO hex_erp odoo.modules.loading: 1 modules loaded in 3.75s, 7930 queries (+7930 extra) 
2025-06-09 11:08:47,650 1 INFO hex_erp odoo.modules.loading: updating modules list 
2025-06-09 11:08:47,651 1 INFO hex_erp odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via n/a 
2025-06-09 11:08:48,153 1 INFO hex_erp odoo.modules.loading: loading 16 modules... 
2025-06-09 11:08:48,153 1 INFO hex_erp odoo.modules.loading: Loading module base_attachment_object_storage (2/16) 
2025-06-09 11:08:48,173 1 INFO hex_erp odoo.modules.registry: module base_attachment_object_storage: creating or updating database tables 
2025-06-09 11:08:48,189 1 INFO hex_erp odoo.modules.loading: loading base_attachment_object_storage/data/res_config_settings_data.xml 
2025-06-09 11:08:48,201 1 INFO hex_erp odoo.modules.loading: Module base_attachment_object_storage loaded in 0.05s, 34 queries (+34 other) 
2025-06-09 11:08:48,202 1 INFO hex_erp odoo.modules.loading: Loading module csms (3/16) 
2025-06-09 11:08:48,221 1 INFO hex_erp odoo.modules.registry: module csms: creating or updating database tables 
2025-06-09 11:08:48,467 1 INFO hex_erp odoo.modules.loading: loading csms/security/security.xml 
2025-06-09 11:08:48,475 1 INFO hex_erp odoo.modules.loading: loading csms/security/ir.model.access.csv 
2025-06-09 11:08:48,480 1 INFO hex_erp odoo.modules.loading: loading csms/views/csms_template.xml 
2025-06-09 11:08:48,495 1 INFO hex_erp odoo.modules.loading: Module csms loaded in 0.29s, 831 queries (+831 other) 
2025-06-09 11:08:48,495 1 INFO hex_erp odoo.modules.loading: Loading module web (4/16) 
2025-06-09 11:08:48,506 1 INFO hex_erp odoo.modules.registry: module web: creating or updating database tables 
2025-06-09 11:08:48,720 1 INFO hex_erp odoo.modules.loading: loading web/security/ir.model.access.csv 
2025-06-09 11:08:48,724 1 INFO hex_erp odoo.modules.loading: loading web/views/webclient_templates.xml 
2025-06-09 11:08:48,744 1 INFO hex_erp odoo.modules.loading: loading web/views/report_templates.xml 
2025-06-09 11:08:48,775 1 INFO hex_erp odoo.modules.loading: loading web/views/base_document_layout_views.xml 
2025-06-09 11:08:48,780 1 INFO hex_erp odoo.modules.loading: loading web/views/partner_view.xml 
2025-06-09 11:08:48,782 1 INFO hex_erp odoo.modules.loading: loading web/views/speedscope_template.xml 
2025-06-09 11:08:48,785 1 INFO hex_erp odoo.modules.loading: loading web/views/neutralize_views.xml 
2025-06-09 11:08:48,787 1 INFO hex_erp odoo.modules.loading: loading web/data/ir_attachment.xml 
2025-06-09 11:08:48,789 1 INFO hex_erp odoo.modules.loading: loading web/data/report_layout.xml 
2025-06-09 11:08:48,835 1 INFO hex_erp odoo.modules.loading: Module web loaded in 0.34s, 991 queries (+991 other) 
2025-06-09 11:08:48,835 1 INFO hex_erp odoo.modules.loading: Loading module auth_totp (5/16) 
2025-06-09 11:08:48,847 1 INFO hex_erp odoo.modules.registry: module auth_totp: creating or updating database tables 
2025-06-09 11:08:48,875 1 INFO hex_erp odoo.modules.loading: loading auth_totp/security/security.xml 
2025-06-09 11:08:48,886 1 INFO hex_erp odoo.modules.loading: loading auth_totp/security/ir.model.access.csv 
2025-06-09 11:08:48,891 1 INFO hex_erp odoo.modules.loading: loading auth_totp/data/ir_action_data.xml 
2025-06-09 11:08:48,894 1 INFO hex_erp odoo.modules.loading: loading auth_totp/views/res_users_views.xml 
2025-06-09 11:08:48,904 1 INFO hex_erp odoo.modules.loading: loading auth_totp/views/templates.xml 
2025-06-09 11:08:48,907 1 INFO hex_erp odoo.modules.loading: loading auth_totp/wizard/auth_totp_wizard_views.xml 
2025-06-09 11:08:48,916 1 INFO hex_erp odoo.modules.loading: Module auth_totp loaded in 0.08s, 179 queries (+179 other) 
2025-06-09 11:08:48,916 1 INFO hex_erp odoo.modules.loading: Loading module base_import (6/16) 
2025-06-09 11:08:48,937 1 INFO hex_erp odoo.modules.registry: module base_import: creating or updating database tables 
2025-06-09 11:08:49,180 1 INFO hex_erp odoo.modules.loading: loading base_import/security/ir.model.access.csv 
2025-06-09 11:08:49,191 1 INFO hex_erp odoo.modules.loading: Module base_import loaded in 0.28s, 806 queries (+806 other) 
2025-06-09 11:08:49,192 1 INFO hex_erp odoo.modules.loading: Loading module base_import_module (7/16) 
2025-06-09 11:08:49,203 1 INFO hex_erp odoo.modules.registry: module base_import_module: creating or updating database tables 
2025-06-09 11:08:49,243 1 INFO hex_erp odoo.modules.loading: loading base_import_module/security/ir.model.access.csv 
2025-06-09 11:08:49,247 1 INFO hex_erp odoo.modules.loading: loading base_import_module/views/base_import_module_view.xml 
2025-06-09 11:08:49,254 1 INFO hex_erp odoo.modules.loading: loading base_import_module/views/ir_module_views.xml 
2025-06-09 11:08:49,273 1 INFO hex_erp odoo.modules.loading: Module base_import_module loaded in 0.08s, 154 queries (+154 other) 
2025-06-09 11:08:49,274 1 INFO hex_erp odoo.modules.loading: Loading module base_setup (8/16) 
2025-06-09 11:08:49,283 1 INFO hex_erp odoo.modules.registry: module base_setup: creating or updating database tables 
2025-06-09 11:08:49,307 1 INFO hex_erp odoo.modules.loading: loading base_setup/data/base_setup_data.xml 
2025-06-09 11:08:49,309 1 INFO hex_erp odoo.modules.loading: loading base_setup/views/res_config_settings_views.xml 
2025-06-09 11:08:49,322 1 INFO hex_erp odoo.modules.loading: loading base_setup/views/res_partner_views.xml 
2025-06-09 11:08:49,332 1 INFO hex_erp odoo.modules.loading: Module base_setup loaded in 0.06s, 139 queries (+139 other) 
2025-06-09 11:08:49,332 1 INFO hex_erp odoo.modules.loading: Loading module bus (9/16) 
2025-06-09 11:08:49,346 1 INFO hex_erp odoo.modules.registry: module bus: creating or updating database tables 
2025-06-09 11:08:49,378 1 INFO hex_erp odoo.modules.loading: loading bus/security/ir.model.access.csv 
2025-06-09 11:08:49,388 1 INFO hex_erp odoo.modules.loading: Module bus loaded in 0.06s, 116 queries (+116 other) 
2025-06-09 11:08:49,388 1 INFO hex_erp odoo.modules.loading: Loading module csms_tencent (10/16) 
2025-06-09 11:08:49,392 1 CRITICAL hex_erp odoo.modules.module: Couldn't load module csms_tencent 
2025-06-09 11:08:49,394 1 WARNING hex_erp odoo.modules.loading: Transient module states were reset 
2025-06-09 11:08:49,394 1 ERROR hex_erp odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "/usr/lib/python3/dist-packages/odoo/modules/registry.py", line 110, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "/usr/lib/python3/dist-packages/odoo/modules/loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "/usr/lib/python3/dist-packages/odoo/modules/loading.py", line 366, in load_marked_modules
    loaded, processed = load_module_graph(
  File "/usr/lib/python3/dist-packages/odoo/modules/loading.py", line 187, in load_module_graph
    load_openerp_module(package.name)
  File "/usr/lib/python3/dist-packages/odoo/modules/module.py", line 395, in load_openerp_module
    __import__(qualname)
  File "/mnt/extra-addons/free-addons/csms_tencent/__init__.py", line 2, in <module>
    from . import models
  File "/mnt/extra-addons/free-addons/csms_tencent/models/__init__.py", line 13, in <module>
    from . import models
  File "/mnt/extra-addons/free-addons/csms_tencent/models/models.py", line 16, in <module>
    from tencentcloud.common import credential
ModuleNotFoundError: No module named 'tencentcloud'
2025-06-09 11:08:49,395 1 CRITICAL hex_erp odoo.service.server: Failed to initialize database `hex_erp`. 
Traceback (most recent call last):
  File "/usr/lib/python3/dist-packages/odoo/service/server.py", line 1369, in preload_registries
    registry = Registry.new(dbname, update_module=update_module)
  File "<decorator-gen-16>", line 2, in new
  File "/usr/lib/python3/dist-packages/odoo/tools/func.py", line 87, in locked
    return func(inst, *args, **kwargs)
  File "/usr/lib/python3/dist-packages/odoo/modules/registry.py", line 110, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "/usr/lib/python3/dist-packages/odoo/modules/loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "/usr/lib/python3/dist-packages/odoo/modules/loading.py", line 366, in load_marked_modules
    loaded, processed = load_module_graph(
  File "/usr/lib/python3/dist-packages/odoo/modules/loading.py", line 187, in load_module_graph
    load_openerp_module(package.name)
  File "/usr/lib/python3/dist-packages/odoo/modules/module.py", line 395, in load_openerp_module
    __import__(qualname)
  File "/mnt/extra-addons/free-addons/csms_tencent/__init__.py", line 2, in <module>
    from . import models
  File "/mnt/extra-addons/free-addons/csms_tencent/models/__init__.py", line 13, in <module>
    from . import models
  File "/mnt/extra-addons/free-addons/csms_tencent/models/models.py", line 16, in <module>
    from tencentcloud.common import credential
ModuleNotFoundError: No module named 'tencentcloud'
2025-06-09 11:08:49,395 1 INFO hex_erp odoo.sql_db: ConnectionPool(used=0/count=0/max=64): Closed 1 connections  
2025-06-09 11:08:49,401 33 INFO hex_erp odoo.service.server: Worker WorkerHTTP (33) alive 
2025-06-09 11:08:49,399 32 INFO hex_erp odoo.service.server: Worker WorkerHTTP (32) alive 
2025-06-09 11:08:49,398 31 INFO hex_erp odoo.service.server: Worker WorkerHTTP (31) alive 
2025-06-09 11:08:49,403 34 INFO hex_erp odoo.service.server: Worker WorkerHTTP (34) alive 
2025-06-09 11:08:49,408 40 INFO hex_erp odoo.service.server: Worker WorkerCron (40) alive 
2025-06-09 11:08:49,409 41 INFO hex_erp odoo.service.server: Worker WorkerCron (41) alive 
2025-06-09 11:08:49,736 35 INFO ? odoo: Odoo version 17.0-20250606 
2025-06-09 11:08:49,736 35 INFO ? odoo: Using configuration file at /etc/odoo/odoo.conf 
2025-06-09 11:08:49,737 35 INFO ? odoo: addons paths: ['/usr/lib/python3/dist-packages/odoo/addons', '/var/lib/odoo/addons/17.0', '/mnt/extra-addons/my-addons', '/mnt/extra-addons/buy-addons', '/mnt/extra-addons/free-addons', '/mnt/extra-addons/enterprise-addons'] 
2025-06-09 11:08:49,737 35 INFO ? odoo: database: odoo@db:5432 
2025-06-09 11:08:49,807 35 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at /usr/local/bin/wkhtmltopdf 
2025-06-09 11:08:49,868 35 INFO ? odoo.service.server: Evented Service (longpolling) running on 0.0.0.0:8072 
2025-06-09 11:09:43,401 34 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "/usr/lib/python3/dist-packages/odoo/tools/config.py", line 790, in session_dir
    os.makedirs(d, 0o700)
  File "/usr/lib/python3.10/os.py", line 225, in makedirs
    mkdir(name, mode)
FileExistsError: [Errno 17] File exists: '/var/lib/odoo/sessions'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3/dist-packages/odoo/http.py", line 2221, in __call__
    request._post_init()
  File "/usr/lib/python3/dist-packages/odoo/http.py", line 1406, in _post_init
    self.session, self.db = self._get_session_and_dbname()
  File "/usr/lib/python3/dist-packages/odoo/http.py", line 1412, in _get_session_and_dbname
    session = root.session_store.new()
  File "/usr/lib/python3/dist-packages/odoo/tools/func.py", line 28, in __get__
    value = self.fget(obj)
  File "/usr/lib/python3/dist-packages/odoo/http.py", line 2147, in session_store
    path = odoo.tools.config.session_dir
  File "/usr/lib/python3/dist-packages/odoo/tools/config.py", line 794, in session_dir
    assert os.access(d, os.W_OK), \
AssertionError: /var/lib/odoo/sessions: directory is not writable
2025-06-09 11:09:43,406 34 INFO ? werkzeug: 192.168.65.1 - - [09/Jun/2025 11:09:43] "HEAD / HTTP/1.1" 500 - 0 0.000 0.019
2025-06-09 11:09:49,546 40 INFO hex_erp odoo.modules.loading: loading 1 modules... 
2025-06-09 11:09:49,546 40 INFO hex_erp odoo.modules.loading: Loading module base (1/1) 
2025-06-09 11:09:49,576 40 INFO hex_erp odoo.modules.registry: module base: creating or updating database tables 
2025-06-09 11:09:49,845 40 INFO hex_erp odoo.modules.loading: loading base/data/res_bank.xml 
2025-06-09 11:09:49,849 40 INFO hex_erp odoo.modules.loading: loading base/data/res.lang.csv 
2025-06-09 11:09:49,941 40 INFO hex_erp odoo.modules.loading: loading base/data/res_lang_data.xml 
2025-06-09 11:09:49,958 40 INFO hex_erp odoo.modules.loading: loading base/data/res_partner_data.xml 
2025-06-09 11:09:49,987 40 INFO hex_erp odoo.modules.loading: loading base/data/res_currency_data.xml 
2025-06-09 11:09:50,223 40 INFO hex_erp odoo.modules.loading: loading base/data/res_company_data.xml 
2025-06-09 11:09:50,228 40 INFO hex_erp odoo.modules.loading: loading base/data/res_users_data.xml 
2025-06-09 11:09:50,552 41 INFO hex_erp odoo.modules.loading: loading 1 modules... 
2025-06-09 11:09:50,553 41 INFO hex_erp odoo.modules.loading: Loading module base (1/1) 
2025-06-09 11:09:50,576 41 INFO hex_erp odoo.modules.registry: module base: creating or updating database tables 
2025-06-09 11:09:51,066 40 INFO hex_erp odoo.modules.loading: loading base/data/report_paperformat_data.xml 
2025-06-09 11:09:51,070 40 INFO hex_erp odoo.modules.loading: loading base/data/res_country_data.xml 
2025-06-09 11:09:51,250 40 INFO hex_erp odoo.modules.loading: loading base/data/ir_demo_data.xml 
2025-06-09 11:09:51,258 40 INFO hex_erp odoo.modules.loading: loading base/security/base_groups.xml 
2025-06-09 11:09:51,456 40 INFO hex_erp odoo.modules.loading: loading base/security/base_security.xml 
2025-06-09 11:09:51,533 40 INFO hex_erp odoo.modules.loading: loading base/views/base_menus.xml 
2025-06-09 11:09:51,569 40 INFO hex_erp odoo.modules.loading: loading base/views/decimal_precision_views.xml 
2025-06-09 11:09:51,578 40 INFO hex_erp odoo.modules.loading: loading base/views/res_config_views.xml 
2025-06-09 11:09:51,586 40 INFO hex_erp odoo.modules.loading: loading base/data/res.country.state.csv 
2025-06-09 11:09:52,227 40 INFO hex_erp odoo.modules.loading: loading base/views/ir_actions_views.xml 
2025-06-09 11:09:52,302 40 INFO hex_erp odoo.modules.loading: loading base/views/ir_asset_views.xml 
2025-06-09 11:09:52,315 40 INFO hex_erp odoo.modules.loading: loading base/views/ir_config_parameter_views.xml 
2025-06-09 11:09:52,326 40 INFO hex_erp odoo.modules.loading: loading base/views/ir_cron_views.xml 
2025-06-09 11:09:52,345 40 INFO hex_erp odoo.modules.loading: loading base/views/ir_cron_trigger_views.xml 
2025-06-09 11:09:52,356 40 INFO hex_erp odoo.modules.loading: loading base/views/ir_filters_views.xml 
2025-06-09 11:09:52,369 40 INFO hex_erp odoo.modules.loading: loading base/views/ir_mail_server_views.xml 
2025-06-09 11:09:52,383 40 INFO hex_erp odoo.modules.loading: loading base/views/ir_model_views.xml 
2025-06-09 11:09:52,476 40 INFO hex_erp odoo.modules.loading: loading base/views/ir_attachment_views.xml 
2025-06-09 11:09:52,491 40 INFO hex_erp odoo.modules.loading: loading base/views/ir_rule_views.xml 
2025-06-09 11:09:52,513 40 INFO hex_erp odoo.modules.loading: loading base/views/ir_sequence_views.xml 
2025-06-09 11:09:52,528 40 INFO hex_erp odoo.modules.loading: loading base/views/ir_ui_menu_views.xml 
2025-06-09 11:09:52,543 40 INFO hex_erp odoo.modules.loading: loading base/views/ir_ui_view_views.xml 
2025-06-09 11:09:52,573 40 INFO hex_erp odoo.modules.loading: loading base/views/ir_default_views.xml 
2025-06-09 11:09:52,587 40 INFO hex_erp odoo.modules.loading: loading base/data/ir_cron_data.xml 
2025-06-09 11:09:52,592 40 WARNING hex_erp odoo.modules.loading: Transient module states were reset 
2025-06-09 11:09:52,592 40 ERROR hex_erp odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "/usr/lib/python3/dist-packages/odoo/modules/registry.py", line 87, in __new__
    return cls.registries[db_name]
  File "<decorator-gen-5>", line 2, in __getitem__
  File "/usr/lib/python3/dist-packages/odoo/tools/func.py", line 87, in locked
    return func(inst, *args, **kwargs)
  File "/usr/lib/python3/dist-packages/odoo/tools/lru.py", line 34, in __getitem__
    a = self.d[obj]
KeyError: 'hex_erp'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_cron.py", line 426, in _try_lock
    self._cr.execute(f"""
  File "/usr/lib/python3/dist-packages/odoo/sql_db.py", line 332, in execute
    res = self._obj.execute(query, params)
psycopg2.errors.LockNotAvailable: could not obtain lock on row in relation "ir_cron"


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 556, in _tag_root
    f(rec)
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 456, in _tag_record
    record = model._load_records([data], self.mode == 'update')
  File "/usr/lib/python3/dist-packages/odoo/models.py", line 5092, in _load_records
    data['record']._load_records_write(data['values'])
  File "/usr/lib/python3/dist-packages/odoo/models.py", line 5023, in _load_records_write
    self.write(values)
  File "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_cron.py", line 439, in write
    self._try_lock()
  File "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_cron.py", line 434, in _try_lock
    raise UserError(_("Record cannot be modified right now: "
odoo.exceptions.UserError: Record cannot be modified right now: This cron task is currently being executed and may not be modified Please try again in a few minutes

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/lib/python3/dist-packages/odoo/modules/registry.py", line 110, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "/usr/lib/python3/dist-packages/odoo/modules/loading.py", line 427, in load_modules
    loaded_modules, processed_modules = load_module_graph(
  File "/usr/lib/python3/dist-packages/odoo/modules/loading.py", line 229, in load_module_graph
    load_data(env, idref, mode, kind='data', package=package)
  File "/usr/lib/python3/dist-packages/odoo/modules/loading.py", line 73, in load_data
    tools.convert_file(env, package.name, filename, idref, mode, noupdate, kind)
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 627, in convert_file
    convert_xml_import(env, module, fp, idref, mode, noupdate)
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 693, in convert_xml_import
    obj.parse(doc.getroot())
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 613, in parse
    self._tag_root(de)
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 569, in _tag_root
    raise ParseError('while parsing %s:%s, somewhere inside\n%s' % (
odoo.tools.convert.ParseError: while parsing /usr/lib/python3/dist-packages/odoo/addons/base/data/ir_cron_data.xml:3, somewhere inside
<record id="autovacuum_job" model="ir.cron">
        <field name="name">Base: Auto-vacuum internal data</field>
        <field name="model_id" ref="model_ir_autovacuum"/>
        <field name="state">code</field>
        <field name="code">model._run_vacuum_cleaner()</field>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
        <field name="priority">3</field>
    </record>
2025-06-09 11:09:52,595 40 WARNING hex_erp odoo.addons.base.models.ir_cron: Exception in cron: 
Traceback (most recent call last):
  File "/usr/lib/python3/dist-packages/odoo/modules/registry.py", line 87, in __new__
    return cls.registries[db_name]
  File "<decorator-gen-5>", line 2, in __getitem__
  File "/usr/lib/python3/dist-packages/odoo/tools/func.py", line 87, in locked
    return func(inst, *args, **kwargs)
  File "/usr/lib/python3/dist-packages/odoo/tools/lru.py", line 34, in __getitem__
    a = self.d[obj]
KeyError: 'hex_erp'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_cron.py", line 426, in _try_lock
    self._cr.execute(f"""
  File "/usr/lib/python3/dist-packages/odoo/sql_db.py", line 332, in execute
    res = self._obj.execute(query, params)
psycopg2.errors.LockNotAvailable: could not obtain lock on row in relation "ir_cron"


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 556, in _tag_root
    f(rec)
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 456, in _tag_record
    record = model._load_records([data], self.mode == 'update')
  File "/usr/lib/python3/dist-packages/odoo/models.py", line 5092, in _load_records
    data['record']._load_records_write(data['values'])
  File "/usr/lib/python3/dist-packages/odoo/models.py", line 5023, in _load_records_write
    self.write(values)
  File "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_cron.py", line 439, in write
    self._try_lock()
  File "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_cron.py", line 434, in _try_lock
    raise UserError(_("Record cannot be modified right now: "
odoo.exceptions.UserError: Record cannot be modified right now: This cron task is currently being executed and may not be modified Please try again in a few minutes

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_cron.py", line 138, in _process_jobs
    registry = odoo.registry(db_name)
  File "/usr/lib/python3/dist-packages/odoo/__init__.py", line 106, in registry
    return modules.registry.Registry(database_name)
  File "/usr/lib/python3/dist-packages/odoo/modules/registry.py", line 89, in __new__
    return cls.new(db_name)
  File "<decorator-gen-16>", line 2, in new
  File "/usr/lib/python3/dist-packages/odoo/tools/func.py", line 87, in locked
    return func(inst, *args, **kwargs)
  File "/usr/lib/python3/dist-packages/odoo/modules/registry.py", line 110, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "/usr/lib/python3/dist-packages/odoo/modules/loading.py", line 427, in load_modules
    loaded_modules, processed_modules = load_module_graph(
  File "/usr/lib/python3/dist-packages/odoo/modules/loading.py", line 229, in load_module_graph
    load_data(env, idref, mode, kind='data', package=package)
  File "/usr/lib/python3/dist-packages/odoo/modules/loading.py", line 73, in load_data
    tools.convert_file(env, package.name, filename, idref, mode, noupdate, kind)
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 627, in convert_file
    convert_xml_import(env, module, fp, idref, mode, noupdate)
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 693, in convert_xml_import
    obj.parse(doc.getroot())
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 613, in parse
    self._tag_root(de)
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 569, in _tag_root
    raise ParseError('while parsing %s:%s, somewhere inside\n%s' % (
odoo.tools.convert.ParseError: while parsing /usr/lib/python3/dist-packages/odoo/addons/base/data/ir_cron_data.xml:3, somewhere inside
<record id="autovacuum_job" model="ir.cron">
        <field name="name">Base: Auto-vacuum internal data</field>
        <field name="model_id" ref="model_ir_autovacuum"/>
        <field name="state">code</field>
        <field name="code">model._run_vacuum_cleaner()</field>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
        <field name="priority">3</field>
    </record>
2025-06-09 11:09:52,737 41 INFO hex_erp odoo.modules.loading: loading base/data/res_bank.xml 
2025-06-09 11:09:52,743 41 INFO hex_erp odoo.modules.loading: loading base/data/res.lang.csv 
2025-06-09 11:09:52,830 41 INFO hex_erp odoo.modules.loading: loading base/data/res_lang_data.xml 
2025-06-09 11:09:52,845 41 INFO hex_erp odoo.modules.loading: loading base/data/res_partner_data.xml 
2025-06-09 11:09:52,870 41 INFO hex_erp odoo.modules.loading: loading base/data/res_currency_data.xml 
2025-06-09 11:09:53,100 41 INFO hex_erp odoo.modules.loading: loading base/data/res_company_data.xml 
2025-06-09 11:09:53,105 41 INFO hex_erp odoo.modules.loading: loading base/data/res_users_data.xml 
2025-06-09 11:09:53,948 41 INFO hex_erp odoo.modules.loading: loading base/data/report_paperformat_data.xml 
2025-06-09 11:09:53,952 41 INFO hex_erp odoo.modules.loading: loading base/data/res_country_data.xml 
2025-06-09 11:09:54,168 41 INFO hex_erp odoo.modules.loading: loading base/data/ir_demo_data.xml 
2025-06-09 11:09:54,177 41 INFO hex_erp odoo.modules.loading: loading base/security/base_groups.xml 
2025-06-09 11:09:54,352 41 INFO hex_erp odoo.modules.loading: loading base/security/base_security.xml 
2025-06-09 11:09:54,417 41 INFO hex_erp odoo.modules.loading: loading base/views/base_menus.xml 
2025-06-09 11:09:54,450 41 INFO hex_erp odoo.modules.loading: loading base/views/decimal_precision_views.xml 
2025-06-09 11:09:54,461 41 INFO hex_erp odoo.modules.loading: loading base/views/res_config_views.xml 
2025-06-09 11:09:54,467 41 INFO hex_erp odoo.modules.loading: loading base/data/res.country.state.csv 
2025-06-09 11:09:55,173 41 INFO hex_erp odoo.modules.loading: loading base/views/ir_actions_views.xml 
2025-06-09 11:09:55,257 41 INFO hex_erp odoo.modules.loading: loading base/views/ir_asset_views.xml 
2025-06-09 11:09:55,272 41 INFO hex_erp odoo.modules.loading: loading base/views/ir_config_parameter_views.xml 
2025-06-09 11:09:55,284 41 INFO hex_erp odoo.modules.loading: loading base/views/ir_cron_views.xml 
2025-06-09 11:09:55,305 41 INFO hex_erp odoo.modules.loading: loading base/views/ir_cron_trigger_views.xml 
2025-06-09 11:09:55,318 41 INFO hex_erp odoo.modules.loading: loading base/views/ir_filters_views.xml 
2025-06-09 11:09:55,332 41 INFO hex_erp odoo.modules.loading: loading base/views/ir_mail_server_views.xml 
2025-06-09 11:09:55,348 41 INFO hex_erp odoo.modules.loading: loading base/views/ir_model_views.xml 
2025-06-09 11:09:55,450 41 INFO hex_erp odoo.modules.loading: loading base/views/ir_attachment_views.xml 
2025-06-09 11:09:55,465 41 INFO hex_erp odoo.modules.loading: loading base/views/ir_rule_views.xml 
2025-06-09 11:09:55,486 41 INFO hex_erp odoo.modules.loading: loading base/views/ir_sequence_views.xml 
2025-06-09 11:09:55,501 41 INFO hex_erp odoo.modules.loading: loading base/views/ir_ui_menu_views.xml 
2025-06-09 11:09:55,514 41 INFO hex_erp odoo.modules.loading: loading base/views/ir_ui_view_views.xml 
2025-06-09 11:09:55,550 41 INFO hex_erp odoo.modules.loading: loading base/views/ir_default_views.xml 
2025-06-09 11:09:55,564 41 INFO hex_erp odoo.modules.loading: loading base/data/ir_cron_data.xml 
2025-06-09 11:09:55,574 41 WARNING hex_erp odoo.modules.loading: Transient module states were reset 
2025-06-09 11:09:55,574 41 ERROR hex_erp odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "/usr/lib/python3/dist-packages/odoo/modules/registry.py", line 87, in __new__
    return cls.registries[db_name]
  File "<decorator-gen-5>", line 2, in __getitem__
  File "/usr/lib/python3/dist-packages/odoo/tools/func.py", line 87, in locked
    return func(inst, *args, **kwargs)
  File "/usr/lib/python3/dist-packages/odoo/tools/lru.py", line 34, in __getitem__
    a = self.d[obj]
KeyError: 'hex_erp'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_cron.py", line 426, in _try_lock
    self._cr.execute(f"""
  File "/usr/lib/python3/dist-packages/odoo/sql_db.py", line 332, in execute
    res = self._obj.execute(query, params)
psycopg2.errors.LockNotAvailable: could not obtain lock on row in relation "ir_cron"


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 556, in _tag_root
    f(rec)
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 456, in _tag_record
    record = model._load_records([data], self.mode == 'update')
  File "/usr/lib/python3/dist-packages/odoo/models.py", line 5092, in _load_records
    data['record']._load_records_write(data['values'])
  File "/usr/lib/python3/dist-packages/odoo/models.py", line 5023, in _load_records_write
    self.write(values)
  File "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_cron.py", line 439, in write
    self._try_lock()
  File "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_cron.py", line 434, in _try_lock
    raise UserError(_("Record cannot be modified right now: "
odoo.exceptions.UserError: Record cannot be modified right now: This cron task is currently being executed and may not be modified Please try again in a few minutes

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/lib/python3/dist-packages/odoo/modules/registry.py", line 110, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "/usr/lib/python3/dist-packages/odoo/modules/loading.py", line 427, in load_modules
    loaded_modules, processed_modules = load_module_graph(
  File "/usr/lib/python3/dist-packages/odoo/modules/loading.py", line 229, in load_module_graph
    load_data(env, idref, mode, kind='data', package=package)
  File "/usr/lib/python3/dist-packages/odoo/modules/loading.py", line 73, in load_data
    tools.convert_file(env, package.name, filename, idref, mode, noupdate, kind)
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 627, in convert_file
    convert_xml_import(env, module, fp, idref, mode, noupdate)
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 693, in convert_xml_import
    obj.parse(doc.getroot())
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 613, in parse
    self._tag_root(de)
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 569, in _tag_root
    raise ParseError('while parsing %s:%s, somewhere inside\n%s' % (
odoo.tools.convert.ParseError: while parsing /usr/lib/python3/dist-packages/odoo/addons/base/data/ir_cron_data.xml:14, somewhere inside
<record id="ir_cron_res_users_deletion" model="ir.cron">
        <field name="name">Base: Portal Users Deletion</field>
        <field name="model_id" ref="base.model_res_users_deletion"/>
        <field name="state">code</field>
        <field name="code">model._gc_portal_users()</field>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
    </record>
2025-06-09 11:09:55,576 41 WARNING hex_erp odoo.addons.base.models.ir_cron: Exception in cron: 
Traceback (most recent call last):
  File "/usr/lib/python3/dist-packages/odoo/modules/registry.py", line 87, in __new__
    return cls.registries[db_name]
  File "<decorator-gen-5>", line 2, in __getitem__
  File "/usr/lib/python3/dist-packages/odoo/tools/func.py", line 87, in locked
    return func(inst, *args, **kwargs)
  File "/usr/lib/python3/dist-packages/odoo/tools/lru.py", line 34, in __getitem__
    a = self.d[obj]
KeyError: 'hex_erp'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_cron.py", line 426, in _try_lock
    self._cr.execute(f"""
  File "/usr/lib/python3/dist-packages/odoo/sql_db.py", line 332, in execute
    res = self._obj.execute(query, params)
psycopg2.errors.LockNotAvailable: could not obtain lock on row in relation "ir_cron"


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 556, in _tag_root
    f(rec)
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 456, in _tag_record
    record = model._load_records([data], self.mode == 'update')
  File "/usr/lib/python3/dist-packages/odoo/models.py", line 5092, in _load_records
    data['record']._load_records_write(data['values'])
  File "/usr/lib/python3/dist-packages/odoo/models.py", line 5023, in _load_records_write
    self.write(values)
  File "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_cron.py", line 439, in write
    self._try_lock()
  File "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_cron.py", line 434, in _try_lock
    raise UserError(_("Record cannot be modified right now: "
odoo.exceptions.UserError: Record cannot be modified right now: This cron task is currently being executed and may not be modified Please try again in a few minutes

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_cron.py", line 138, in _process_jobs
    registry = odoo.registry(db_name)
  File "/usr/lib/python3/dist-packages/odoo/__init__.py", line 106, in registry
    return modules.registry.Registry(database_name)
  File "/usr/lib/python3/dist-packages/odoo/modules/registry.py", line 89, in __new__
    return cls.new(db_name)
  File "<decorator-gen-16>", line 2, in new
  File "/usr/lib/python3/dist-packages/odoo/tools/func.py", line 87, in locked
    return func(inst, *args, **kwargs)
  File "/usr/lib/python3/dist-packages/odoo/modules/registry.py", line 110, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "/usr/lib/python3/dist-packages/odoo/modules/loading.py", line 427, in load_modules
    loaded_modules, processed_modules = load_module_graph(
  File "/usr/lib/python3/dist-packages/odoo/modules/loading.py", line 229, in load_module_graph
    load_data(env, idref, mode, kind='data', package=package)
  File "/usr/lib/python3/dist-packages/odoo/modules/loading.py", line 73, in load_data
    tools.convert_file(env, package.name, filename, idref, mode, noupdate, kind)
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 627, in convert_file
    convert_xml_import(env, module, fp, idref, mode, noupdate)
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 693, in convert_xml_import
    obj.parse(doc.getroot())
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 613, in parse
    self._tag_root(de)
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 569, in _tag_root
    raise ParseError('while parsing %s:%s, somewhere inside\n%s' % (
odoo.tools.convert.ParseError: while parsing /usr/lib/python3/dist-packages/odoo/addons/base/data/ir_cron_data.xml:14, somewhere inside
<record id="ir_cron_res_users_deletion" model="ir.cron">
        <field name="name">Base: Portal Users Deletion</field>
        <field name="model_id" ref="base.model_res_users_deletion"/>
        <field name="state">code</field>
        <field name="code">model._gc_portal_users()</field>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
    </record>
2025-06-09 11:09:55,800 31 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "/usr/lib/python3/dist-packages/odoo/tools/config.py", line 790, in session_dir
    os.makedirs(d, 0o700)
  File "/usr/lib/python3.10/os.py", line 225, in makedirs
    mkdir(name, mode)
FileExistsError: [Errno 17] File exists: '/var/lib/odoo/sessions'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3/dist-packages/odoo/http.py", line 2221, in __call__
    request._post_init()
  File "/usr/lib/python3/dist-packages/odoo/http.py", line 1406, in _post_init
    self.session, self.db = self._get_session_and_dbname()
  File "/usr/lib/python3/dist-packages/odoo/http.py", line 1411, in _get_session_and_dbname
    if not sid or not root.session_store.is_valid_key(sid):
  File "/usr/lib/python3/dist-packages/odoo/tools/func.py", line 28, in __get__
    value = self.fget(obj)
  File "/usr/lib/python3/dist-packages/odoo/http.py", line 2147, in session_store
    path = odoo.tools.config.session_dir
  File "/usr/lib/python3/dist-packages/odoo/tools/config.py", line 794, in session_dir
    assert os.access(d, os.W_OK), \
AssertionError: /var/lib/odoo/sessions: directory is not writable
2025-06-09 11:09:55,863 31 INFO ? werkzeug: 192.168.65.1 - - [09/Jun/2025 11:09:55] "GET / HTTP/1.1" 500 - 0 0.000 0.070
2025-06-09 11:09:56,792 33 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "/usr/lib/python3/dist-packages/odoo/tools/config.py", line 790, in session_dir
    os.makedirs(d, 0o700)
  File "/usr/lib/python3.10/os.py", line 225, in makedirs
    mkdir(name, mode)
FileExistsError: [Errno 17] File exists: '/var/lib/odoo/sessions'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3/dist-packages/odoo/http.py", line 2221, in __call__
    request._post_init()
  File "/usr/lib/python3/dist-packages/odoo/http.py", line 1406, in _post_init
    self.session, self.db = self._get_session_and_dbname()
  File "/usr/lib/python3/dist-packages/odoo/http.py", line 1411, in _get_session_and_dbname
    if not sid or not root.session_store.is_valid_key(sid):
  File "/usr/lib/python3/dist-packages/odoo/tools/func.py", line 28, in __get__
    value = self.fget(obj)
  File "/usr/lib/python3/dist-packages/odoo/http.py", line 2147, in session_store
    path = odoo.tools.config.session_dir
  File "/usr/lib/python3/dist-packages/odoo/tools/config.py", line 794, in session_dir
    assert os.access(d, os.W_OK), \
AssertionError: /var/lib/odoo/sessions: directory is not writable
2025-06-09 11:09:56,810 33 INFO ? werkzeug: 192.168.65.1 - - [09/Jun/2025 11:09:56] "GET /favicon.ico HTTP/1.1" 500 - 0 0.000 0.055
2025-06-09 11:10:12,572 1 INFO hex_erp odoo.service.server: Stopping gracefully 
2025-06-09 11:10:12,576 31 INFO hex_erp odoo.service.server: Worker (31) exiting. request_count: 1, registry count: 0. 
2025-06-09 11:10:12,577 33 INFO hex_erp odoo.service.server: Worker (33) exiting. request_count: 1, registry count: 0. 
2025-06-09 11:10:12,578 34 INFO hex_erp odoo.service.server: Worker (34) exiting. request_count: 1, registry count: 0. 
2025-06-09 11:10:12,578 32 INFO hex_erp odoo.service.server: Worker (32) exiting. request_count: 0, registry count: 0. 
2025-06-09 11:10:12,581 40 INFO hex_erp odoo.service.server: Worker (40) exiting. request_count: 1, registry count: 0. 
2025-06-09 11:10:12,591 41 INFO hex_erp odoo.service.server: Worker (41) exiting. request_count: 1, registry count: 0. 
