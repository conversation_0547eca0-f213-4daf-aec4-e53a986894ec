

/* ## CSS error message ##*/
body::before {
  font-weight: bold;
  content: "A css error occured, using an old style to render this page";
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 100000000000;
  background-color: #C00;
  color: #DDD;
}

css_error_message {
  content: "Error: Undefined variable: \"$gray-200\".\A        on line 1476:21 of /stdin\A>> \A\A   --------------------^\AThis error occurred while compiling the bundle 'web.assets_frontend' containing:\A    - /web/static/lib/bootstrap/scss/_functions.scss\A    - /web/static/lib/bootstrap/scss/_mixins.scss\A    - /web/static/src/scss/functions.scss\A    - /web/static/src/scss/mixins_forwardport.scss\A    - /web/static/src/scss/bs_mixins_overrides.scss\A    - /web/static/src/scss/utils.scss\A    - /web/static/src/scss/primary_variables.scss\A    - /web/static/src/core/avatar/avatar.variables.scss\A    - /web/static/src/core/notifications/notification.variables.scss\A    - /web/static/src/search/control_panel/control_panel.variables.scss\A    - /web/static/src/search/search_panel/search_panel.variables.scss\A    - /web/static/src/views/fields/statusbar/statusbar_field.variables.scss\A    - /web/static/src/views/form/form.variables.scss\A    - /web/static/src/views/kanban/kanban.variables.scss\A    - /web/static/src/webclient/burger_menu/burger_menu.variables.scss\A    - /web/static/src/webclient/navbar/navbar.variables.scss\A    - /web/static/src/scss/secondary_variables.scss\A    - /web/static/src/scss/bootstrap_overridden_frontend.scss\A    - /web/static/src/scss/pre_variables.scss\A    - /web/static/lib/bootstrap/scss/_variables.scss\A    - /web/static/src/scss/import_bootstrap.scss\A    - /web/static/src/scss/helpers_backport.scss\A    - /web/static/src/scss/utilities_custom.scss\A    - /web/static/lib/bootstrap/scss/utilities/_api.scss\A    - /web/static/src/scss/bootstrap_review.scss\A    - /web/static/src/scss/bootstrap_review_frontend.scss\A    - /web/static/src/webclient/navbar/navbar.scss\A    - /web/static/src/scss/animation.scss\A    - /web/static/src/scss/base_frontend.scss\A    - /web/static/src/scss/fontawesome_overridden.scss\A    - /web/static/src/scss/mimetypes.scss\A    - /web/static/src/scss/ui.scss\A    - /web/static/src/views/fields/translation_dialog.scss\A    - /web/static/src/views/fields/signature/signature_field.scss\A    - /web/static/src/legacy/scss/ui.scss\A    - /web/static/src/legacy/scss/modal.scss\A    - /web/static/src/legacy/scss/lazyloader.scss\A    - /web/static/src/core/utils/transitions.scss\A    - /web/static/src/core/action_swiper/action_swiper.scss\A    - /web/static/src/core/autocomplete/autocomplete.scss\A    - /web/static/src/core/avatar/avatar.scss\A    - /web/static/src/core/checkbox/checkbox.scss\A    - /web/static/src/core/colorlist/colorlist.scss\A    - /web/static/src/core/colorpicker/colorpicker.scss\A    - /web/static/src/core/datetime/datetime_picker.scss\A    - /web/static/src/core/debug/debug_menu.scss\A    - /web/static/src/core/debug/profiling/profiling_item.scss\A    - /web/static/src/core/debug/profiling/profiling_qweb.scss\A    - /web/static/src/core/dialog/dialog.scss\A    - /web/static/src/core/dropdown/accordion_item.scss\A    - /web/static/src/core/dropdown/dropdown.scss\A    - /web/static/src/core/effects/rainbow_man.scss\A    - /web/static/src/core/emoji_picker/emoji_picker.dark.scss\A    - /web/static/src/core/emoji_picker/emoji_picker.scss\A    - /web/static/src/core/errors/error_dialog.scss\A    - /web/static/src/core/file_upload/file_upload_progress_bar.scss\A    - /web/static/src/core/file_upload/file_upload_progress_record.scss\A    - /web/static/src/core/file_viewer/file_viewer.scss\A    - /web/static/src/core/install_prompt/install_prompt.scss\A    - /web/static/src/core/model_field_selector/model_field_selector.scss\A    - /web/static/src/core/model_field_selector/model_field_selector_popover.scss\A    - /web/static/src/core/model_selector/model_selector.scss\A    - /web/static/src/core/notebook/notebook.scss\A    - /web/static/src/core/notifications/notification.scss\A    - /web/static/src/core/popover/popover.scss\A    - /web/static/src/core/record_selectors/record_selectors.scss\A    - /web/static/src/core/resizable_panel/resizable_panel.scss\A    - /web/static/src/core/select_menu/select_menu.scss\A    - /web/static/src/core/signature/name_and_signature.scss\A    - /web/static/src/core/tags_list/tags_list.scss\A    - /web/static/src/core/tooltip/tooltip.scss\A    - /web/static/src/core/tree_editor/tree_editor.scss\A    - /web/static/src/core/ui/block_ui.scss\A    - /web/static/src/core/utils/draggable_hook_builder.scss\A    - /web/static/src/core/utils/nested_sortable.scss";
}
