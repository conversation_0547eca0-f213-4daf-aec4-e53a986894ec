version: '3.8'

services:
  # PostgreSQL 数据库
  db:
    image: postgres:14
    container_name: hex_erp_db_simple
    environment:
      POSTGRES_DB: hex_erp
      POSTGRES_USER: odoo
      POSTGRES_PASSWORD: odoo
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5433:5432"
    restart: unless-stopped

  # Odoo 17 应用
  odoo:
    image: odoo:17.0
    container_name: hex_erp_odoo_simple
    depends_on:
      - db
    environment:
      - HOST=db
      - USER=odoo
      - PASSWORD=odoo
    ports:
      - "8071:8069"
    volumes:
      # 映射插件目录
      - ./my-addons:/mnt/extra-addons/my-addons
      - ./buy-addons:/mnt/extra-addons/buy-addons
      - ./free-addons:/mnt/extra-addons/free-addons
      - ./enterprise-addons:/mnt/extra-addons/enterprise-addons
      # 映射数据目录
      - ./filestore:/var/lib/odoo/filestore
      - ./logs:/var/log/odoo
    restart: unless-stopped

volumes:
  postgres_data:
