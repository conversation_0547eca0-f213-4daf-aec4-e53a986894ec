Metadata-Version: 2.1
Name: vobject
Version: 0.9.9
Summary: A full-featured Python package for parsing and creating iCalendar and vCard files
Home-page: UNKNOWN
Author: <PERSON>
Author-email: jef<PERSON>@osafoundation.org
Maintainer: <PERSON>-email: david<PERSON>@pobox.com
License: Apache
Project-URL: Home, http://py-vobject.github.io/
Project-URL: GitHub, https://github.com/py-vobject/vobject
Project-URL: Download, https://github.com/py-vobject/vobject/releases
Project-URL: Issues, https://github.com/py-vobject/vobject/issues
Keywords: vobject,icalendar,vcard,ics,vcs,hcalendar
Platform: any
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Natural Language :: English
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Topic :: Text Processing
Requires-Dist: pytz
Requires-Dist: six
Requires-Dist: python-dateutil>=2.5.0; python_version < "3.10"
Requires-Dist: python-dateutil>=2.7.0; python_version >= "3.10"


Description
-----------

Parses iCalendar and vCard files into Python data structures, decoding the
relevant encodings. Also serializes vobject data structures to iCalendar, vCard,
or (experimentally) hCalendar unicode strings.

Requirements
------------

Requires python 2.7 or later, dateutil 2.4.0 or later and six.

Recent changes
--------------
    - Revert too-strict serialization of timestamp values - broke too many other
       implementations

For older changes, see
   - http://py-vobject.github.io/#release-history or
   - http://vobject.skyhouseconsulting.com/history.html

