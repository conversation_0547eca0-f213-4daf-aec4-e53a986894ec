widths = {'A': 667,
 'AE': 1000,
 'Aacute': 667,
 'Acircumflex': 667,
 'Adieresis': 667,
 'Agrave': 667,
 'Aring': 667,
 'Atilde': 667,
 'B': 667,
 'C': 722,
 '<PERSON><PERSON><PERSON>': 722,
 'D': 722,
 'E': 667,
 'Eacute': 667,
 'Ecircumflex': 667,
 'Edieresis': 667,
 'Egrave': 667,
 'Eth': 722,
 'Euro': 556,
 'F': 611,
 'G': 778,
 'H': 722,
 'I': 278,
 'Iacute': 278,
 'Icircumflex': 278,
 'Idieresis': 278,
 'Igrave': 278,
 'J': 500,
 'K': 667,
 'L': 556,
 'Lslash': 556,
 'M': 833,
 'N': 722,
 'Ntilde': 722,
 'O': 778,
 'OE': 1000,
 'Oacute': 778,
 'Ocircumflex': 778,
 'Odieresis': 778,
 'Ograve': 778,
 'Oslash': 778,
 'Otilde': 778,
 'P': 667,
 'Q': 778,
 'R': 722,
 '<PERSON>': 667,
 '<PERSON>aron': 667,
 'T': 611,
 'Thorn': 667,
 'U': 722,
 'Uacute': 722,
 'Ucircumflex': 722,
 'Udieresis': 722,
 'Ugrave': 722,
 'V': 667,
 'W': 944,
 'X': 667,
 'Y': 667,
 'Yacute': 667,
 'Ydieresis': 667,
 'Z': 611,
 'Zcaron': 611,
 'a': 556,
 'aacute': 556,
 'acircumflex': 556,
 'acute': 333,
 'adieresis': 556,
 'ae': 889,
 'agrave': 556,
 'ampersand': 667,
 'aring': 556,
 'asciicircum': 469,
 'asciitilde': 584,
 'asterisk': 389,
 'at': 1015,
 'atilde': 556,
 'b': 556,
 'backslash': 278,
 'bar': 260,
 'braceleft': 334,
 'braceright': 334,
 'bracketleft': 278,
 'bracketright': 278,
 'breve': 333,
 'brokenbar': 260,
 'bullet': 350,
 'c': 500,
 'caron': 333,
 'ccedilla': 500,
 'cedilla': 333,
 'cent': 556,
 'circumflex': 333,
 'colon': 278,
 'comma': 278,
 'copyright': 737,
 'currency': 556,
 'd': 556,
 'dagger': 556,
 'daggerdbl': 556,
 'degree': 400,
 'dieresis': 333,
 'divide': 584,
 'dollar': 556,
 'dotaccent': 333,
 'dotlessi': 278,
 'e': 556,
 'eacute': 556,
 'ecircumflex': 556,
 'edieresis': 556,
 'egrave': 556,
 'eight': 556,
 'ellipsis': 1000,
 'emdash': 1000,
 'endash': 556,
 'equal': 584,
 'eth': 556,
 'exclam': 278,
 'exclamdown': 333,
 'f': 278,
 'fi': 500,
 'five': 556,
 'fl': 500,
 'florin': 556,
 'four': 556,
 'fraction': 167,
 'g': 556,
 'germandbls': 611,
 'grave': 333,
 'greater': 584,
 'guillemotleft': 556,
 'guillemotright': 556,
 'guilsinglleft': 333,
 'guilsinglright': 333,
 'h': 556,
 'hungarumlaut': 333,
 'hyphen': 333,
 'i': 222,
 'iacute': 278,
 'icircumflex': 278,
 'idieresis': 278,
 'igrave': 278,
 'j': 222,
 'k': 500,
 'l': 222,
 'less': 584,
 'logicalnot': 584,
 'lslash': 222,
 'm': 833,
 'macron': 333,
 'minus': 584,
 'mu': 556,
 'multiply': 584,
 'n': 556,
 'nine': 556,
 'ntilde': 556,
 'numbersign': 556,
 'o': 556,
 'oacute': 556,
 'ocircumflex': 556,
 'odieresis': 556,
 'oe': 944,
 'ogonek': 333,
 'ograve': 556,
 'one': 556,
 'onehalf': 834,
 'onequarter': 834,
 'onesuperior': 333,
 'ordfeminine': 370,
 'ordmasculine': 365,
 'oslash': 611,
 'otilde': 556,
 'p': 556,
 'paragraph': 537,
 'parenleft': 333,
 'parenright': 333,
 'percent': 889,
 'period': 278,
 'periodcentered': 278,
 'perthousand': 1000,
 'plus': 584,
 'plusminus': 584,
 'q': 556,
 'question': 556,
 'questiondown': 611,
 'quotedbl': 355,
 'quotedblbase': 333,
 'quotedblleft': 333,
 'quotedblright': 333,
 'quoteleft': 222,
 'quoteright': 222,
 'quotesinglbase': 222,
 'quotesingle': 191,
 'r': 333,
 'registered': 737,
 'ring': 333,
 's': 500,
 'scaron': 500,
 'section': 556,
 'semicolon': 278,
 'seven': 556,
 'six': 556,
 'slash': 278,
 'space': 278,
 'sterling': 556,
 't': 278,
 'thorn': 556,
 'three': 556,
 'threequarters': 834,
 'threesuperior': 333,
 'tilde': 333,
 'trademark': 1000,
 'two': 556,
 'twosuperior': 333,
 'u': 556,
 'uacute': 556,
 'ucircumflex': 556,
 'udieresis': 556,
 'ugrave': 556,
 'underscore': 556,
 'v': 500,
 'w': 722,
 'x': 500,
 'y': 500,
 'yacute': 500,
 'ydieresis': 500,
 'yen': 556,
 'z': 500,
 'zcaron': 500,
 'zero': 556}
