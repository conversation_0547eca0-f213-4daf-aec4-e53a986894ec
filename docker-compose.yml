version: '3.8'

services:
  # PostgreSQL 数据库
  db:
    image: postgres:14
    container_name: hex_erp_db
    environment:
      POSTGRES_DB: hex_erp
      POSTGRES_USER: odoo
      POSTGRES_PASSWORD: odoo
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data/pgdata
    ports:
      - '5432:5432'
    restart: unless-stopped
    networks:
      - hex_erp_network

  # Odoo 17 应用
  odoo:
    image: odoo:17.0
    container_name: hex_erp_odoo
    depends_on:
      - db
    environment:
      - HOST=db
      - USER=odoo
      - PASSWORD=odoo
    ports:
      - '8070:8069'
    volumes:
      # 映射插件目录
      - ./my-addons:/mnt/extra-addons/my-addons
      - ./buy-addons:/mnt/extra-addons/buy-addons
      - ./free-addons:/mnt/extra-addons/free-addons
      - ./enterprise-addons:/mnt/extra-addons/enterprise-addons
      # 映射配置文件
      - ./docker-odoo.conf:/etc/odoo/odoo.conf
      # 映射数据目录
      - ./filestore:/var/lib/odoo/filestore
      - ./logs:/var/log/odoo
      # 映射会话存储
      - odoo_sessions:/var/lib/odoo/sessions
    restart: unless-stopped
    networks:
      - hex_erp_network
    command: odoo -c /etc/odoo/odoo.conf -i base

  # Redis (可选，用于会话存储)
  redis:
    image: redis:7-alpine
    container_name: hex_erp_redis
    ports:
      - '6379:6379'
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - hex_erp_network

volumes:
  postgres_data:
  odoo_sessions:
  redis_data:

networks:
  hex_erp_network:
    driver: bridge
