# 🎉 Hex ERP Docker 环境已成功配置！

## ✅ 当前状态

您的 Hex ERP (Odoo 17) 系统已经成功在 Docker 中运行！

### 🌐 访问信息
- **Web 界面**: http://localhost:8071
- **数据库端口**: 5433 (PostgreSQL)
- **管理员密码**: admin123

### 📦 运行的服务
- **Odoo 17**: 主应用服务
- **PostgreSQL 14**: 数据库服务

## 🚀 使用方法

### 快速启动
```bash
./quick-start.sh
```

### 手动管理
```bash
# 启动服务
docker-compose -f docker-compose-simple.yml up -d

# 停止服务
docker-compose -f docker-compose-simple.yml down

# 查看日志
docker-compose -f docker-compose-simple.yml logs -f

# 重启服务
docker-compose -f docker-compose-simple.yml restart
```

### 查看服务状态
```bash
docker-compose -f docker-compose-simple.yml ps
```

## 📁 插件配置

您的插件目录已经映射到 Odoo 容器中：
- `./my-addons` → `/mnt/extra-addons/my-addons`
- `./buy-addons` → `/mnt/extra-addons/buy-addons`
- `./free-addons` → `/mnt/extra-addons/free-addons`
- `./enterprise-addons` → `/mnt/extra-addons/enterprise-addons`

## 🔧 常用操作

### 安装插件
1. 将插件放入相应的目录
2. 重启 Odoo 服务：
   ```bash
   docker-compose -f docker-compose-simple.yml restart odoo
   ```
3. 在 Odoo 界面中安装插件

### 数据库备份
```bash
docker exec hex_erp_db_simple pg_dump -U odoo hex_erp > backup.sql
```

### 数据库恢复
```bash
docker exec -i hex_erp_db_simple psql -U odoo -d hex_erp < backup.sql
```

### 进入容器
```bash
# 进入 Odoo 容器
docker exec -it hex_erp_odoo_simple bash

# 进入数据库容器
docker exec -it hex_erp_db_simple bash
```

## 📋 下一步

1. **访问系统**: 打开 http://localhost:8071
2. **创建数据库**: 首次访问时会提示创建数据库
3. **安装插件**: 在应用管理中安装需要的插件
4. **配置系统**: 根据业务需求配置 ERP 系统

## 🛠️ 故障排除

### 如果服务无法启动
```bash
# 查看详细日志
docker-compose -f docker-compose-simple.yml logs

# 重新构建
docker-compose -f docker-compose-simple.yml up --build -d
```

### 如果端口冲突
修改 `docker-compose-simple.yml` 中的端口映射：
```yaml
ports:
  - "8072:8069"  # 改为其他端口
```

### 清理和重置
```bash
# 停止并删除容器
docker-compose -f docker-compose-simple.yml down

# 删除数据卷（注意：会丢失数据）
docker-compose -f docker-compose-simple.yml down -v

# 重新启动
docker-compose -f docker-compose-simple.yml up -d
```

## 🎯 成功要点

✅ Docker 环境已配置  
✅ Odoo 17 已安装并运行  
✅ PostgreSQL 数据库已配置  
✅ 插件目录已映射  
✅ 服务可以正常访问  

您现在可以开始使用 Hex ERP 系统了！
